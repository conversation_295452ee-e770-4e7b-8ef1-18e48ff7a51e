import { fields<PERSON><PERSON><PERSON><PERSON> } from "@/modules/inspection/atoms/forms/fields-form.atom";
import { addFieldToGroupAtom, removeGroup<PERSON><PERSON>, updateGroupTitleAtom } from "@/modules/inspection/atoms/forms/group-fields.atom";
import { createDefaultField } from "@/modules/inspection/constants/form/default-field-value";
import { getGroupColorClasses } from "@/modules/inspection/lib/utils/get-group-color";
import { Button } from "@/shared/components/shadcn/button";
import { Input } from "@/shared/components/shadcn/input";
import { TableCell, TableRow } from "@/shared/components/shadcn/table";
import { useAtomValue, useSetAtom } from "jotai";
import { Edit, FolderMinus, Plus, Trash2, Users } from "lucide-react";
import { useState } from "react";

interface IGroupHeaderProps {
	group: { id: number; title: string; tempId: string };
	colSpan: number;
	indexInGroup: number;
}

export const GroupHeader: React.FC<IGroupHeaderProps> = ({ group, colSpan, indexInGroup }) => {
	const [isEditing, setIsEditing] = useState(false);
	const [editTitle, setEditTitle] = useState(group.title);
	const fields = useAtomValue(fieldsFormAtom);
	const updateGroupTitle = useSetAtom(updateGroupTitleAtom);
	const removeGroup = useSetAtom(removeGroupAtom);
	const addFieldToGroup = useSetAtom(addFieldToGroupAtom);

	const groupHeaderClass = getGroupColorClasses(indexInGroup, "light");
	const groupAccentClass = getGroupColorClasses(indexInGroup, "accent");
	const groupMediumClass = getGroupColorClasses(indexInGroup, "medium");

	const handleSaveTitle = () => {
		updateGroupTitle({ tempId: group.tempId, title: editTitle });
		setIsEditing(false);
	};

	const handleCancelEdit = () => {
		setEditTitle(group.title);
		setIsEditing(false);
	};

	const handleRemoveGroup = () => removeGroup(group.tempId);

	const handleAddFieldToGroup = () => {
		const newField = createDefaultField(fields);
		addFieldToGroup({ groupId: group.id, newField });
	};

	return (
		<TableRow
			className={`border-b-4 ${groupHeaderClass} hover:bg-opacity-70 shadow-sm transition-all duration-200 ease-in-out`.trim().replace(/\s+/g, " ")}
		>
			<TableCell colSpan={colSpan} className="py-4">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-4">
						<div className={`rounded-full p-2 ${groupMediumClass} shadow-sm ring-1 ring-white`.trim().replace(/\s+/g, " ")}>
							<FolderMinus className="size-5 text-white" />
						</div>
						{isEditing ? (
							<div className="flex items-center gap-3">
								<Input
									value={editTitle}
									onChange={e => setEditTitle(e.target.value)}
									className="h-9 w-80 font-medium shadow-sm focus:ring-2"
									placeholder="Título do grupo"
									onKeyDown={e => {
										if (e.key === "Enter") {
											handleSaveTitle();
										} else if (e.key === "Escape") {
											handleCancelEdit();
										}
									}}
									autoFocus
								/>
								<Button size="sm" onClick={handleSaveTitle} className="h-9 px-4 shadow-sm">
									Salvar
								</Button>
								<Button size="sm" variant="outline" onClick={handleCancelEdit} className="h-9 px-4 shadow-sm">
									Cancelar
								</Button>
							</div>
						) : (
							<div className="flex items-center gap-3">
								<span
									className="cursor-pointer text-lg font-semibold text-gray-800 transition-colors duration-150 hover:text-gray-900"
									onClick={() => setIsEditing(true)}
									title="Clique para editar o título do grupo"
								>
									{group.title || "Grupo sem título"}
								</span>
								<Button
									size="sm"
									variant="ghost"
									onClick={() => setIsEditing(true)}
									className="h-8 w-8 rounded-md p-0 text-gray-600 transition-all duration-150 hover:bg-gray-100 hover:text-gray-800"
									title="Editar título do grupo"
								>
									<Edit className="size-4" />
								</Button>
							</div>
						)}
					</div>
					<div className="flex items-center gap-3">
						<Button
							size="sm"
							variant="outline"
							onClick={e => {
								e.preventDefault();
								handleAddFieldToGroup();
							}}
							className={`h-9 border-2 px-4 font-medium shadow-sm transition-all duration-200 hover:shadow-md ${groupAccentClass} hover:bg-opacity-90 border-transparent text-white`
								.trim()
								.replace(/\s+/g, " ")}
							title="Adicionar campo ao grupo"
						>
							<Plus className="mr-2 size-4" />
							Adicionar Campo
						</Button>
						<Button
							size="sm"
							onClick={e => {
								e.preventDefault();
								handleRemoveGroup();
							}}
							className="h-9 w-9 rounded-md !bg-white p-0 text-red-600 shadow-sm transition-all duration-200 hover:bg-red-100 hover:text-red-800 hover:shadow-md"
							title="Remover grupo"
						>
							<Trash2 className="size-4" />
						</Button>
					</div>
				</div>
			</TableCell>
		</TableRow>
	);
};
