import { useGroupManagement } from "@/modules/inspection/hooks/form/create/group-management.hook";
import { useInspectionFormTableField } from "@/modules/inspection/hooks/form/create/table-field.hook";
import { Button } from "@/shared/components/shadcn/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { closestCenter, DndContext } from "@dnd-kit/core";
import { restrictToParentElement, restrictToVerticalAxis } from "@dnd-kit/modifiers";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { flexRender } from "@tanstack/react-table";
import { Plus, Users } from "lucide-react";
import { Fragment } from "react";
import { inspectionFormColumns } from "./columns/columns";
import { GroupHeader } from "./group/group-header";
import { GroupedRow } from "./group/grouped-row";

export const NewTable: React.FC = () => {
	const { table, sensors, sortableId, dataIds, handleDragEnd, handleDragStart } = useInspectionFormTableField();
	const { groups, groupedFields, addGroup, addStandaloneField } = useGroupManagement();

	return (
		<div className="mt-2 flex flex-col gap-4">
			<div className="hidden w-full justify-end lg:flex">
				<div className="flex gap-2">
					<Button
						variant="outline"
						onClick={e => {
							e.preventDefault();
							addGroup();
						}}
					>
						<Users className="ml-2 size-4" />
						Adicionar Grupo
					</Button>
					<Button
						variant="outline"
						onClick={e => {
							e.preventDefault();
							addStandaloneField();
						}}
					>
						<Plus className="ml-2 size-4" />
						Adicionar campo
					</Button>
				</div>
			</div>

			<div className="hidden h-full w-full overflow-auto rounded-lg border lg:block">
				<DndContext
					collisionDetection={closestCenter}
					modifiers={[restrictToVerticalAxis, restrictToParentElement]}
					onDragEnd={handleDragEnd}
					onDragStart={handleDragStart}
					sensors={sensors}
					id={sortableId}
				>
					<Table>
						<TableHeader className="bg-muted sticky top-0 z-10">
							{table.getHeaderGroups().map(headerGroup => (
								<TableRow key={headerGroup.id}>
									{headerGroup.headers.map(header => {
										return (
											<TableHead key={header.id} colSpan={header.colSpan}>
												{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
											</TableHead>
										);
									})}
								</TableRow>
							))}
						</TableHeader>
						<TableBody className="**:data-[slot=table-cell]:first:w-8">
							{table.getRowModel().rows?.length || groups.length ? (
								<SortableContext items={dataIds} strategy={verticalListSortingStrategy}>
									{groupedFields.get(undefined)?.map(field => {
										const row = table.getRowModel().rows.find(r => r.original.tempId === field.tempId);
										return row ? <GroupedRow key={row.id} row={row} isGrouped={false} indexInGroup={0} /> : null;
									})}
									{groups.map((group, indexInGroup) => (
										<Fragment key={group.tempId}>
											<GroupHeader indexInGroup={indexInGroup + 1} group={group} colSpan={inspectionFormColumns.length} />
											{groupedFields.get(group.id)?.map(field => {
												const row = table.getRowModel().rows.find(r => r.original.tempId === field.tempId);
												return row ? <GroupedRow indexInGroup={indexInGroup + 1} key={row.id} row={row} isGrouped={true} /> : null;
											})}
										</Fragment>
									))}
								</SortableContext>
							) : (
								<TableRow>
									<TableCell colSpan={inspectionFormColumns.length} className="h-24 text-center">
										Nenhum resultado.
									</TableCell>
								</TableRow>
							)}
						</TableBody>
					</Table>
				</DndContext>
			</div>
		</div>
	);
};
