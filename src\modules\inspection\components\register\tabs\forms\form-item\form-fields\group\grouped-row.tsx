import { fieldSelect<PERSON>tom, selectedFieldDataAtom } from "@/modules/inspection/atoms/forms/fields-form.atom";
import { InspectionFormTypeEnum } from "@/modules/inspection/constants/form/type-enum";
import { getGroupColorClasses } from "@/modules/inspection/lib/utils/get-group-color";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { TableCell, TableRow } from "@/shared/components/shadcn/table";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { flexRender, Row } from "@tanstack/react-table";
import { useAtomValue, useSetAtom } from "jotai";
import { GripVertical } from "lucide-react";
import { InspectionFieldOptions } from "../options/field-options";

interface IGroupedRowProps {
	row: Row<ICreateFieldForm>;
	isGrouped?: boolean;
	indexInGroup: number;
}

export const GroupedRow: React.FC<IGroupedRowProps> = ({ row, isGrouped = false, indexInGroup }) => {
	const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
		id: row.original.tempId,
	});
	const setSelectedRow = useSetAtom(fieldSelectAtom);
	const selected = useAtomValue(selectedFieldDataAtom);
	const showOptions = selected?.tempId === row.original.tempId && row.original.typeId === InspectionFormTypeEnum.OPTIONS;

	// Classes de estilo para o grupo
	const groupBorderClass = isGrouped ? getGroupColorClasses(indexInGroup, "light") : "";
	const groupIndicatorClass = isGrouped ? getGroupColorClasses(indexInGroup, "medium") : "";
	const groupLineClass = isGrouped ? getGroupColorClasses(indexInGroup, "line") : "";

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
		opacity: isDragging ? 0.5 : 1,
	};

	return (
		<>
			<TableRow
				ref={setNodeRef}
				style={style}
				onClick={() => setSelectedRow(row.original.tempId)}
				className={`transition-all duration-200 ease-in-out ${isDragging ? "z-50 shadow-lg" : ""} ${isGrouped ? `border-l-4 ${groupBorderClass}` : ""} ${!isGrouped ? "hover:bg-muted/50" : "hover:bg-muted/30"} ${isGrouped ? "from-muted/20 bg-gradient-to-r to-transparent" : ""} `
					.trim()
					.replace(/\s+/g, " ")}
			>
				{row.getVisibleCells().map((cell, index) => {
					if (cell.column.id === "drag-handle") {
						return (
							<TableCell key={cell.id} className={`${isGrouped ? "pl-8" : "pl-2"} w-8`}>
								<div className="flex items-center gap-3">
									{isGrouped && (
										<div className="flex h-4 w-4 items-center justify-center">
											<div className={`h-3 w-3 rounded-full shadow-sm ${groupIndicatorClass} ring-1 ring-white`} />
										</div>
									)}
									<button
										{...attributes}
										{...listeners}
										className="hover:bg-muted/70 cursor-grab rounded-md p-2 transition-colors duration-150 hover:shadow-sm active:cursor-grabbing"
										title="Arrastar para reordenar"
									>
										<GripVertical className="text-muted-foreground size-4" />
									</button>
								</div>
							</TableCell>
						);
					}
					return (
						<TableCell
							key={cell.id}
							className={` ${isGrouped && index === 1 ? "pl-8" : ""} ${cell.column.id === "field-name" && isGrouped ? "relative" : ""} transition-colors duration-150`
								.trim()
								.replace(/\s+/g, " ")}
						>
							{cell.column.id === "field-name" && isGrouped && (
								<div
									className={`absolute top-1/2 left-3 h-px w-4 -translate-y-1/2 transform ${groupLineClass} opacity-60`
										.trim()
										.replace(/\s+/g, " ")}
								/>
							)}
							{flexRender(cell.column.columnDef.cell, cell.getContext())}
						</TableCell>
					);
				})}
			</TableRow>
			{showOptions && (
				<TableRow className="border-t-0">
					<TableCell colSpan={row.getVisibleCells().length} className="bg-muted/30 border-t-0">
						<div className="py-2">
							<InspectionFieldOptions row={row} />
						</div>
					</TableCell>
				</TableRow>
			)}
		</>
	);
};
